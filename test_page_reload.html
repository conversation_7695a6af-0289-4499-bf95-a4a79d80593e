<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест обновления счетчиков при перезагрузке</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .button {
            background: #FFC107;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .button:hover {
            background: #FFD54F;
        }
        .counter-display {
            background: #444;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 18px;
            text-align: center;
        }
        .ad-counter {
            font-size: 16px;
            color: #FFC107;
            margin: 5px 0;
        }
        .log {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px;
            display: inline-block;
        }
        .status.success { background: #4CAF50; }
        .status.warning { background: #FF9800; }
        .status.error { background: #F44336; }
    </style>
</head>
<body>
    <h1>🔄 Тест обновления счетчиков при перезагрузке</h1>

    <div class="test-section">
        <h2>📊 Имитация кнопок рекламы</h2>
        <div class="counter-display">
            <div><strong>Автопереходы по баннеру:</strong></div>
            <div class="ad-counter" id="native-banner-counter">осталось 20 показов</div>
        </div>
        <div class="counter-display">
            <div><strong>Видеореклама:</strong></div>
            <div class="ad-counter" id="rewarded-video-counter">осталось 20 показов</div>
        </div>
        <div class="counter-display">
            <div><strong>Интерстициальные баннеры:</strong></div>
            <div class="ad-counter" id="interstitial-counter">осталось 20 показов</div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Тестирование</h2>
        <button class="button" onclick="simulateAdView('native_banner')">📈 Просмотр баннера</button>
        <button class="button" onclick="simulateAdView('rewarded_video')">📺 Просмотр видео</button>
        <button class="button" onclick="simulateAdView('interstitial')">📱 Просмотр интерстициала</button>
        <br><br>
        <button class="button" onclick="reloadPage()">🔄 Перезагрузить страницу</button>
        <button class="button" onclick="checkCounters()">🔍 Проверить счетчики</button>
        <button class="button" onclick="resetCounters()">🗑️ Сбросить счетчики</button>
    </div>

    <div class="test-section">
        <h2>📈 Статус инициализации</h2>
        <div id="init-status">
            <span class="status warning">Загрузка...</span>
        </div>
    </div>

    <div class="test-section">
        <h2>📝 Лог событий</h2>
        <div class="log" id="event-log">
            Лог событий будет отображаться здесь...
        </div>
    </div>

    <script>
        // Мини-версия локализации
        window.appLocalization = {
            isLoaded: true,
            currentLanguage: 'ru',
            translations: {
                'ru': {
                    'tasks.ad_views_left': 'осталось {count} показов',
                    'tasks.ad_views_left_single': 'остался {count} показ',
                    'tasks.ad_views_left_few': 'осталось {count} показа'
                }
            },
            get: function(key, params = {}) {
                const translation = this.translations[this.currentLanguage]?.[key] || key;
                return translation.replace(/\{(\w+)\}/g, (match, param) => params[param] || match);
            }
        };

        function logEvent(message) {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateInitStatus(status, type = 'warning') {
            const statusDiv = document.getElementById('init-status');
            statusDiv.innerHTML = `<span class="status ${type}">${status}</span>`;
        }

        function simulateAdView(adType) {
            logEvent(`🎬 Имитация просмотра рекламы: ${adType}`);
            
            if (window.adCountersManager) {
                const beforeCount = window.adCountersManager.getTodayAdCount(adType);
                window.adCountersManager.incrementCounter(adType);
                const afterCount = window.adCountersManager.getTodayAdCount(adType);
                
                logEvent(`📊 ${adType}: ${beforeCount} → ${afterCount}`);
            } else {
                logEvent('❌ AdCountersManager не найден');
            }
        }

        function checkCounters() {
            if (!window.adCountersManager) {
                logEvent('❌ AdCountersManager не найден');
                return;
            }

            const info = window.adCountersManager.getAllLimitsInfo();
            logEvent('📊 Текущее состояние счетчиков:');
            
            Object.keys(info).forEach(key => {
                if (key !== '_resetInfo') {
                    const data = info[key];
                    logEvent(`  ${key}: ${data.current}/${data.limit} (осталось: ${data.remaining})`);
                }
            });
        }

        function resetCounters() {
            if (window.adCountersManager) {
                window.adCountersManager.resetAllCounters();
                logEvent('🔄 Все счетчики сброшены');
            }
        }

        function reloadPage() {
            logEvent('🔄 Перезагрузка страницы...');
            setTimeout(() => {
                location.reload();
            }, 500);
        }

        // Отслеживание загрузки модулей
        let initStartTime = Date.now();

        function checkModuleLoad() {
            if (window.adCountersManager) {
                const loadTime = Date.now() - initStartTime;
                updateInitStatus(`✅ AdCountersManager загружен (${loadTime}ms)`, 'success');
                logEvent(`✅ AdCountersManager найден через ${loadTime}ms`);
                
                // Проверяем инициализацию
                const checkInit = setInterval(() => {
                    if (window.adCountersManager.isInitialized) {
                        clearInterval(checkInit);
                        const initTime = Date.now() - initStartTime;
                        updateInitStatus(`✅ Полностью инициализирован (${initTime}ms)`, 'success');
                        logEvent(`✅ AdCountersManager инициализирован через ${initTime}ms`);
                        checkCounters();
                    }
                }, 50);
                
                return true;
            }
            return false;
        }

        // Проверяем загрузку каждые 50ms
        const loadChecker = setInterval(() => {
            if (checkModuleLoad()) {
                clearInterval(loadChecker);
            }
        }, 50);

        // Таймаут на случай если модуль не загрузится
        setTimeout(() => {
            if (!window.adCountersManager) {
                updateInitStatus('❌ Модуль не загрузился', 'error');
                logEvent('❌ AdCountersManager не загрузился за 10 секунд');
            }
        }, 10000);

        // Перехватываем console.log
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.join(' ');
            
            if (message.includes('[AdCounters]')) {
                logEvent(`🔍 ${message}`);
            }
        };

        // Инициализация
        window.addEventListener('load', () => {
            logEvent('🚀 Страница загружена');
            updateInitStatus('🔄 Ожидание модулей...', 'warning');
        });

        logEvent('📄 Скрипт инициализирован');
    </script>

    <!-- Подключаем модуль счетчиков -->
    <script src="js/ad-counters.js"></script>
</body>
</html>
